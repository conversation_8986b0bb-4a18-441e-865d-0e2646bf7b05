import {
  forwardRef,
  useCallback,
  useState,
  useImperativeHandle,
  useEffect,
  useRef,
  useMemo,
} from 'react';
import clsx from 'clsx';
import { useElementSize } from '@antscorp/antsomi-ui/es/hooks';

import { FontSizeInputProps, HandlerRef } from './types';
import { DEFAULT_FONT_SIZE, DEFAULT_FONT_SIZE_OPTIONS } from './constants';
import { CLS } from './utils';
import { FontSizeDropdown } from './components/FontSizeDropdown';
import { FontSizeControl } from './components/FontSizeControl';

export const FontSizeInput = forwardRef<HandlerRef, FontSizeInputProps>((props, ref) => {
  const {
    min = 1,
    max = 300,
    value: valueProp,
    defaultValue = DEFAULT_FONT_SIZE,
    options = DEFAULT_FONT_SIZE_OPTIONS,
    placeholder,
    style,
    className,
    disabled,
    dropdownProps,
    onFocus,
    onBlur,
    onChange = () => undefined,
  } = props;

  const [uncontrolledValue, setUncontrolledValue] = useState(defaultValue);
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const [popoverValue, setPopoverValue] = useState<number | null>(null);
  const [inputRef, { width: inputWidth }] = useElementSize<HTMLInputElement>();

  const isControlled = valueProp !== undefined;
  const value = isControlled ? +valueProp : +uncontrolledValue;

  const popoverItemRefs = useRef<Map<number, HTMLLIElement>>(new Map());

  const fontSizeOptions = useMemo(
    () =>
      options.map(option => ({
        size: option,
        label: option.toString(),
        className: clsx(CLS.PopoverListItem, {
          [CLS.PopoverListItemSelected]: option === value,
        }),
      })),
    [options, value],
  );

  useEffect(() => {
    if (!isPopoverOpen) {
      setPopoverValue(null);
    }

    if (isPopoverOpen && popoverValue) {
      popoverItemRefs.current.get(popoverValue)?.focus({
        preventScroll: true,
      });
    }
  }, [popoverValue, isPopoverOpen]);

  const handleInputFocus = (event: React.FocusEvent<HTMLInputElement>) => {
    onFocus?.(value, event);
    setIsPopoverOpen(true);

    if (fontSizeOptions.some(item => item.size === value)) {
      setPopoverValue(value);
    }
  };

  const handleInputBlur = (event: React.FocusEvent<HTMLInputElement>) => {
    onBlur?.(value, event);

    if (!popoverValue) {
      setIsPopoverOpen(false);
    }
  };

  const handleValueChange = useCallback(
    (newValue: number, info: { type: 'input' | 'dropdown' }) => {
      if (!isControlled) {
        setUncontrolledValue(newValue);
      }

      onChange(newValue, info);
    },
    [isControlled, onChange],
  );

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      const currentSize = popoverValue ?? value;
      const currentFocusIndex = fontSizeOptions.findIndex(item => item.size === currentSize);

      const firstFontSize = fontSizeOptions.at(0);
      const lastFontSize = fontSizeOptions.at(-1);

      switch (e.key) {
        case 'Enter': {
          handleValueChange(uncontrolledValue, { type: 'input' });
          inputRef.current?.blur();
          break;
        }

        case 'ArrowDown': {
          e.preventDefault();
          if (currentFocusIndex !== -1) {
            const nextFocusIndex = Math.min(fontSizeOptions.length - 1, currentFocusIndex + 1);
            setPopoverValue(fontSizeOptions[nextFocusIndex].size);
          } else if (firstFontSize) {
            setPopoverValue(firstFontSize.size);
          }
          break;
        }

        case 'ArrowUp': {
          e.preventDefault();
          if (currentFocusIndex !== -1) {
            const nextFocusIndex = Math.max(0, currentFocusIndex - 1);
            setPopoverValue(fontSizeOptions[nextFocusIndex].size);
          } else if (lastFontSize) {
            setPopoverValue(lastFontSize.size);
          }
          break;
        }

        default:
          break;
      }
    },
    [fontSizeOptions, inputRef, value, popoverValue, uncontrolledValue, handleValueChange],
  );

  const handleClickPopoverItem = useCallback(
    (size: number) => {
      handleValueChange(size, { type: 'dropdown' });
      setIsPopoverOpen(false);
      inputRef.current?.blur();
    },
    [handleValueChange, inputRef],
  );

  const handlePopoverItemKeydown = useCallback(
    (e: React.KeyboardEvent, size: number) => {
      const currentFocusIndex = fontSizeOptions.findIndex(item => item.size === popoverValue);

      switch (e.key) {
        case 'Enter': {
          handleClickPopoverItem(size);
          break;
        }

        case 'ArrowDown': {
          const nextFocusIndex = Math.min(fontSizeOptions.length - 1, currentFocusIndex + 1);
          setPopoverValue(fontSizeOptions[nextFocusIndex].size);
          e.stopPropagation();
          break;
        }

        case 'ArrowUp': {
          const nextFocusIndex = Math.max(0, currentFocusIndex - 1);
          setPopoverValue(fontSizeOptions[nextFocusIndex].size);
          e.stopPropagation();
          break;
        }

        default:
          break;
      }
    },
    [fontSizeOptions, popoverValue, handleClickPopoverItem],
  );

  useImperativeHandle(ref, () => ({
    closeDropdown: () => setIsPopoverOpen(false),
  }));

  const content = (
    <FontSizeControl
      disabled={disabled}
      ref={inputRef}
      className={className}
      value={popoverValue ?? value}
      max={max}
      min={min}
      keyboard={false}
      defaultValue={defaultValue}
      style={style}
      placeholder={placeholder}
      onFocus={handleInputFocus}
      onBlur={handleInputBlur}
      onKeyDown={handleKeyDown}
      onChange={newValue => {
        if (newValue === null) return;

        setUncontrolledValue(Number(newValue));
      }}
      onIncrement={() => handleValueChange(value + 1, { type: 'input' })}
      onDecrement={() => handleValueChange(value - 1, { type: 'input' })}
    />
  );

  if (options.length) {
    return (
      <FontSizeDropdown
        isOpen={isPopoverOpen}
        options={fontSizeOptions}
        popoverValue={popoverValue}
        width={inputWidth}
        itemRefs={popoverItemRefs}
        onItemClick={handleClickPopoverItem}
        onItemKeyDown={handlePopoverItemKeydown}
        onClose={() => setIsPopoverOpen(false)}
        getPopupContainer={() => dropdownProps?.getPopupContainer?.() || document.body}
      >
        {content}
      </FontSizeDropdown>
    );
  }

  return content;
});
