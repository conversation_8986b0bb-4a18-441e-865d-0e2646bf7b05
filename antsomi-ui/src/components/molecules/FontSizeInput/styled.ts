import { THEME } from '@antscorp/antsomi-ui/es/constants';
import { Button } from 'antd';
import styled from 'styled-components';

export const StyledListFont = styled.ul`
  margin: 0;
  padding: 0;
  list-style-type: none;
`;

export const StyledListItemFont = styled.li`
  padding: 6px 10px;
  cursor: pointer;
  text-align: center;

  &:focus-visible {
    outline: none;
  }

  &:hover,
  &:focus,
  &:focus-visible {
    background-color: ${THEME.components?.Menu?.itemHoverBg};
  }
`;

export const InputRoot = styled.div`
  gap: 2px;
  display: flex;
  align-items: center;
  width: fit-content;
`;

export const ControlButton = styled(Button)`
  &.antsomi-btn {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    padding: 0px;
  }
`;
